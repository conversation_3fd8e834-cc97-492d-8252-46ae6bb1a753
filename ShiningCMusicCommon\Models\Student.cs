using System.ComponentModel.DataAnnotations;

namespace ShiningCMusicCommon.Models
{
    public class Student
    {
        public int StudentId { get; set; }
        
        [StringLength(50)]
        public string? StudentName { get; set; }
        
        [StringLength(250)]
        public string? Email { get; set; }
        
        public DateTime CreatedUTC { get; set; } = DateTime.UtcNow;
        
        public DateTime? UpdatedUTC { get; set; }
        
        public bool IsArchived { get; set; } = false;

        public bool ExcludeEmail { get; set; } = false;

        public int? TutorID { get; set; }

        public int? SubjectId { get; set; }

        [StringLength(20)]
        public string? LoginName { get; set; }

        // Computed property for remaining lessons (not stored in database)
        public int RemainingLessons { get; set; } = 0;

        // Computed properties for grid filtering/sorting (not stored in database)
        public string TutorName { get; set; } = string.Empty;
        public string SubjectName { get; set; } = string.Empty;

        // Navigation properties
        public virtual Tutor? Tutor { get; set; }
        public virtual Subject? Subject { get; set; }
        public virtual User? User { get; set; }
        public virtual ICollection<Lesson> Lessons { get; set; } = new List<Lesson>();
    }
}
