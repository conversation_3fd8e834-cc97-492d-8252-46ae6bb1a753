using System;
using ShiningCMusicCommon.Enums;

namespace ShiningCMusicCommon.Extensions
{
    public static class BackgroundProcessorExtensions
    {
        public static string GetDisplayName(this BackgroundProcessor processor)
        {
            return processor switch
            {
                BackgroundProcessor.LessonCleanup => BackgroundProcessorKeys.LessonCleanupDisplay,
                BackgroundProcessor.TutorCleanup => BackgroundProcessorKeys.TutorCleanupDisplay,
                BackgroundProcessor.StudentCleanup => BackgroundProcessorKeys.StudentCleanupDisplay,
                BackgroundProcessor.TimesheetCleanup => BackgroundProcessorKeys.TimesheetCleanupDisplay,
                BackgroundProcessor.PaymentReminder => BackgroundProcessorKeys.PaymentReminderDisplay,
                BackgroundProcessor.EmailProcessor => BackgroundProcessorKeys.EmailProcessorDisplay,
                _ => processor.ToString()
            };
        }

        public static string GetDescription(this BackgroundProcessor processor)
        {
            return processor switch
            {
                BackgroundProcessor.LessonCleanup => BackgroundProcessorKeys.LessonCleanupDescription,
                BackgroundProcessor.TutorCleanup => BackgroundProcessorKeys.TutorCleanupDescription,
                BackgroundProcessor.StudentCleanup => BackgroundProcessorKeys.StudentCleanupDescription,
                BackgroundProcessor.TimesheetCleanup => BackgroundProcessorKeys.TimesheetCleanupDescription,
                BackgroundProcessor.PaymentReminder => BackgroundProcessorKeys.PaymentReminderDescription,
                BackgroundProcessor.EmailProcessor => BackgroundProcessorKeys.EmailProcessorDescription,
                _ => string.Empty
            };
        }

        public static string ToConfigKey(this BackgroundProcessor processor)
        {
            return processor switch
            {
                BackgroundProcessor.LessonCleanup => BackgroundProcessorKeys.LessonCleanup,
                BackgroundProcessor.TutorCleanup => BackgroundProcessorKeys.TutorCleanup,
                BackgroundProcessor.StudentCleanup => BackgroundProcessorKeys.StudentCleanup,
                BackgroundProcessor.TimesheetCleanup => BackgroundProcessorKeys.TimesheetCleanup,
                BackgroundProcessor.PaymentReminder => BackgroundProcessorKeys.PaymentReminder,
                BackgroundProcessor.EmailProcessor => BackgroundProcessorKeys.EmailProcessor,
                _ => processor.ToString()
            };
        }

        public static string ToEnabledConfigKey(this BackgroundProcessor processor)
        {
            return processor switch
            {
                BackgroundProcessor.LessonCleanup => BackgroundProcessorKeys.LessonCleanupEnabled,
                BackgroundProcessor.TutorCleanup => BackgroundProcessorKeys.TutorCleanupEnabled,
                BackgroundProcessor.StudentCleanup => BackgroundProcessorKeys.StudentCleanupEnabled,
                BackgroundProcessor.TimesheetCleanup => BackgroundProcessorKeys.TimesheetCleanupEnabled,
                BackgroundProcessor.PaymentReminder => BackgroundProcessorKeys.PaymentReminderEnabled,
                BackgroundProcessor.EmailProcessor => BackgroundProcessorKeys.EmailProcessorEnabled,
                _ => $"{processor}Enabled"
            };
        }
    }
}
