using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShiningCMusicBFF.Services;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicBFF.Controllers
{
    [ApiController]
    [Route("bff/tutors")]
    [Authorize]
    public class TutorsController : ControllerBase
    {
        private readonly ApiClientService _apiClient;
        private readonly ILogger<TutorsController> _logger;

        public TutorsController(ApiClientService apiClient, ILogger<TutorsController> logger)
        {
            _apiClient = apiClient;
            _logger = logger;
        }

        // GET: bff/tutors
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Tutor>>> GetTutors()
        {
            try
            {
                _logger.LogInformation("Fetching tutors from API");
                var tutors = await _apiClient.GetJsonAsync<List<Tutor>>("tutors");
                return Ok(tutors ?? new List<Tutor>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving tutors from API");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // GET: bff/tutors/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<Tutor>> GetTutor(int id)
        {
            try
            {
                _logger.LogInformation("Fetching tutor {TutorId} from API", id);
                var tutor = await _apiClient.GetJsonAsync<Tutor>($"tutors/{id}");
                
                if (tutor == null)
                {
                    return NotFound(new { message = "Tutor not found" });
                }

                return Ok(tutor);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving tutor {TutorId} from API", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // POST: bff/tutors
        [HttpPost]
        public async Task<ActionResult<Tutor>> CreateTutor([FromBody] Tutor tutor)
        {
            try
            {
                _logger.LogInformation("Creating tutor via API");
                var response = await _apiClient.PostAsync("tutors", tutor);
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var createdTutor = System.Text.Json.JsonSerializer.Deserialize<Tutor>(content, new System.Text.Json.JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return CreatedAtAction(nameof(GetTutor), new { id = createdTutor?.TutorId }, createdTutor);
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to create tutor. Status: {StatusCode}, Error: {Error}", response.StatusCode, errorContent);
                return StatusCode((int)response.StatusCode, new { message = "Failed to create tutor", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating tutor via API");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // PUT: bff/tutors/{id}
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateTutor(int id, [FromBody] Tutor tutor)
        {
            try
            {
                _logger.LogInformation("Updating tutor {TutorId} via API", id);
                var response = await _apiClient.PutAsync($"tutors/{id}", tutor);
                
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { message = "Tutor updated successfully" });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to update tutor {TutorId}. Status: {StatusCode}, Error: {Error}", id, response.StatusCode, errorContent);
                
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "Tutor not found" });
                }

                return StatusCode((int)response.StatusCode, new { message = "Failed to update tutor", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating tutor {TutorId} via API", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // DELETE: bff/tutors/{id}
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteTutor(int id)
        {
            try
            {
                _logger.LogInformation("Deleting tutor {TutorId} via API", id);
                var response = await _apiClient.DeleteAsync($"tutors/{id}");
                
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { message = "Tutor deleted successfully" });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to delete tutor {TutorId}. Status: {StatusCode}, Error: {Error}", id, response.StatusCode, errorContent);
                
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "Tutor not found" });
                }

                return StatusCode((int)response.StatusCode, new { message = "Failed to delete tutor", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting tutor {TutorId} via API", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // GET: bff/tutors/by-subject/{subjectId}
        [HttpGet("by-subject/{subjectId}")]
        public async Task<ActionResult<IEnumerable<Tutor>>> GetTutorsBySubject(int subjectId)
        {
            try
            {
                _logger.LogInformation("Fetching tutors by subject {SubjectId} from API", subjectId);
                var tutors = await _apiClient.GetJsonAsync<List<Tutor>>($"tutors/by-subject/{subjectId}");
                return Ok(tutors ?? new List<Tutor>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving tutors by subject {SubjectId} from API", subjectId);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // GET: bff/tutors/archived
        [HttpGet("archived")]
        public async Task<ActionResult<IEnumerable<Tutor>>> GetArchivedTutors()
        {
            try
            {
                _logger.LogInformation("Fetching archived tutors from API");
                var tutors = await _apiClient.GetJsonAsync<List<Tutor>>("tutors/archived");
                return Ok(tutors ?? new List<Tutor>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving archived tutors from API");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // PUT: bff/tutors/{id}/archive
        [HttpPut("{id}/archive")]
        public async Task<IActionResult> ArchiveTutor(int id)
        {
            try
            {
                _logger.LogInformation("Archiving tutor {TutorId} via API", id);
                var response = await _apiClient.PutAsync($"tutors/{id}/archive", null);
                
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { message = "Tutor archived successfully" });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to archive tutor {TutorId}. Status: {StatusCode}, Error: {Error}", id, response.StatusCode, errorContent);
                
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "Tutor not found" });
                }

                return StatusCode((int)response.StatusCode, new { message = "Failed to archive tutor", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error archiving tutor {TutorId} via API", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // PUT: bff/tutors/{id}/restore
        [HttpPut("{id}/restore")]
        public async Task<IActionResult> RestoreTutor(int id)
        {
            try
            {
                _logger.LogInformation("Restoring tutor {TutorId} via API", id);
                var response = await _apiClient.PutAsync($"tutors/{id}/restore", null);
                
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { message = "Tutor restored successfully" });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to restore tutor {TutorId}. Status: {StatusCode}, Error: {Error}", id, response.StatusCode, errorContent);
                
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "Tutor not found" });
                }

                return StatusCode((int)response.StatusCode, new { message = "Failed to restore tutor", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error restoring tutor {TutorId} via API", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // GET: bff/tutors/{id}/remaining-lessons
        [HttpGet("{id}/remaining-lessons")]
        public async Task<ActionResult<int>> GetRemainingLessons(int id)
        {
            try
            {
                _logger.LogInformation("Fetching remaining lessons for tutor {TutorId} from API", id);
                var remainingLessons = await _apiClient.GetJsonAsync<int>($"tutors/{id}/remaining-lessons");
                return Ok(remainingLessons);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving remaining lessons for tutor {TutorId} from API", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }
    }
}
