/*
==============================================================================
SHINING C MUSIC - FIXED DATABASE MIGRATION SCRIPT
==============================================================================
Script Name: AddAccessLevelToUserRoles_Fixed.sql
Purpose: Add AccessLevel column to UserRoles table (Fixes parsing issues)
Version: 1.0
Date: 2025-01-09

DESCRIPTION:
This version uses dynamic SQL and GO statements to avoid parsing issues
where SQL Server tries to validate column references before the column exists.

Run this script step by step, or copy each section separately.
==============================================================================
*/

PRINT 'Starting AccessLevel migration for UserRoles table...'
PRINT 'Step 1: Adding AccessLevel column...'

-- Step 1: Add AccessLevel column if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[UserRoles]') AND name = 'AccessLevel')
BEGIN
    ALTER TABLE [dbo].[UserRoles]
    ADD [AccessLevel] [int] NOT NULL DEFAULT 10
    PRINT '✓ AccessLevel column added successfully'
END
ELSE
BEGIN
    PRINT '⚠ AccessLevel column already exists'
END
GO

-- Step 2: Update existing roles with appropriate access levels
PRINT 'Step 2: Updating existing roles...'

-- Update Administrator role (ID = 1)
UPDATE [dbo].[UserRoles] 
SET [AccessLevel] = 100 
WHERE [ID] = 1
PRINT '✓ Administrator role updated to AccessLevel 100'

-- Update Tutor role (ID = 2)  
UPDATE [dbo].[UserRoles] 
SET [AccessLevel] = 20 
WHERE [ID] = 2
PRINT '✓ Tutor role updated to AccessLevel 20'

-- Update Student role (ID = 3)
UPDATE [dbo].[UserRoles] 
SET [AccessLevel] = 10 
WHERE [ID] = 3
PRINT '✓ Student role updated to AccessLevel 10'
GO

-- Step 3: Add Manager role if it doesn't exist
PRINT 'Step 3: Adding Manager role...'

IF NOT EXISTS (SELECT 1 FROM [dbo].[UserRoles] WHERE [ID] = 4)
BEGIN
    INSERT INTO [dbo].[UserRoles] ([ID], [Description], [AccessLevel])
    VALUES (4, 'Manager', 80)
    PRINT '✓ Manager role created with AccessLevel 80'
END
ELSE
BEGIN
    -- Update existing Manager role to ensure correct access level
    UPDATE [dbo].[UserRoles] 
    SET [AccessLevel] = 80 
    WHERE [ID] = 4
    PRINT '✓ Existing Manager role updated to AccessLevel 80'
END
GO

-- Step 4: Add constraint to ensure AccessLevel is within valid range
PRINT 'Step 4: Adding constraint...'

IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_UserRoles_AccessLevel_Range')
BEGIN
    ALTER TABLE [dbo].[UserRoles]
    ADD CONSTRAINT CK_UserRoles_AccessLevel_Range 
    CHECK ([AccessLevel] >= 1 AND [AccessLevel] <= 100)
    PRINT '✓ AccessLevel range constraint added (1-100)'
END
ELSE
BEGIN
    PRINT '⚠ AccessLevel range constraint already exists'
END
GO

-- Step 5: Final verification
PRINT 'Step 5: Verification...'
PRINT 'Current UserRoles:'

SELECT 
    [ID], 
    [Description], 
    [AccessLevel],
    CASE 
        WHEN [AccessLevel] = 100 THEN 'Administrator (Full Access)'
        WHEN [AccessLevel] = 80 THEN 'Manager (Management Access)'
        WHEN [AccessLevel] = 20 THEN 'Tutor (Teaching Access)'
        WHEN [AccessLevel] = 10 THEN 'Student (Basic Access)'
        ELSE 'Custom Access Level'
    END AS [Access_Description]
FROM [dbo].[UserRoles] 
ORDER BY [AccessLevel] DESC, [ID]

PRINT ''
PRINT '✓ Migration completed successfully!'
PRINT 'Ready for application deployment!'
GO
