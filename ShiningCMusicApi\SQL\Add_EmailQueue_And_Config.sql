USE [MusicSchool]
GO

-- Create EmailQueue table
IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[EmailQueue]') AND type in (N'U'))
DROP TABLE [dbo].[EmailQueue]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[EmailQueue](
	[ID] [int] IDENTITY(1,1) NOT NULL,
	[ToEmail] [nvarchar](256) NULL,
	[CC] [nvarchar](256) NULL,
	[BCC] [nvarchar](256) NULL,
	[Subject] [nvarchar](500) NULL,
	[BodyText] [nvarchar](max) NULL,
	[BodyHtml] [nvarchar](max) NULL,
	[CreatedUTC] [datetime] NOT NULL DEFAULT GETUTCDATE(),
	[DeliverAfterUTC] [datetime] NOT NULL DEFAULT GETUTCDATE(),
	[SentAttempts] [int] NOT NULL DEFAULT 0,
	[SentUTC] [datetime] NULL,
	[Cancelled] [bit] NOT NULL DEFAULT 0,
 CONSTRAINT [PK_EmailQueue] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- Add indexes for performance
CREATE NONCLUSTERED INDEX [IX_EmailQueue_Pending] ON [dbo].[EmailQueue]
(
	[Cancelled] ASC,
	[SentUTC] ASC,
	[DeliverAfterUTC] ASC
)
INCLUDE([SentAttempts]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO

CREATE NONCLUSTERED INDEX [IX_EmailQueue_Cleanup] ON [dbo].[EmailQueue]
(
	[SentUTC] ASC,
	[CreatedUTC] ASC
) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO

PRINT 'EmailQueue table created successfully with indexes.'

-- Add Email Processor configurations (GroupId: 200)
-- Check if configurations already exist to avoid duplicates
IF NOT EXISTS (SELECT 1 FROM [dbo].[Config] WHERE [GroupId] = 200 AND [Key] = 'EmailProcessorEnabled')
BEGIN
    INSERT INTO [dbo].[Config] ([GroupId], [Key], [Value], [Description], [DataType])
    VALUES (200, 'EmailProcessorEnabled', 'true', 'Enable/disable email processor background service', 'bool');
    PRINT 'Added EmailProcessorEnabled configuration.';
END
ELSE
BEGIN
    PRINT 'EmailProcessorEnabled configuration already exists.';
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Config] WHERE [GroupId] = 200 AND [Key] = 'EmailProcessorFrequencyMinutes')
BEGIN
    INSERT INTO [dbo].[Config] ([GroupId], [Key], [Value], [Description], [DataType])
    VALUES (200, 'EmailProcessorFrequencyMinutes', '1', 'Email processor frequency in minutes', 'int');
    PRINT 'Added EmailProcessorFrequencyMinutes configuration.';
END
ELSE
BEGIN
    PRINT 'EmailProcessorFrequencyMinutes configuration already exists.';
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Config] WHERE [GroupId] = 200 AND [Key] = 'EmailProcessorMaxAttempts')
BEGIN
    INSERT INTO [dbo].[Config] ([GroupId], [Key], [Value], [Description], [DataType])
    VALUES (200, 'EmailProcessorMaxAttempts', '5', 'Maximum email send attempts before giving up', 'int');
    PRINT 'Added EmailProcessorMaxAttempts configuration.';
END
ELSE
BEGIN
    PRINT 'EmailProcessorMaxAttempts configuration already exists.';
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Config] WHERE [GroupId] = 200 AND [Key] = 'EmailProcessorCleanupDays')
BEGIN
    INSERT INTO [dbo].[Config] ([GroupId], [Key], [Value], [Description], [DataType])
    VALUES (200, 'EmailProcessorCleanupDays', '30', 'Days to keep sent emails before cleanup', 'int');
    PRINT 'Added EmailProcessorCleanupDays configuration.';
END
ELSE
BEGIN
    PRINT 'EmailProcessorCleanupDays configuration already exists.';
END

PRINT 'Email processor configurations added successfully.';

-- Display current email processor configurations
SELECT 
    [ConfigId],
    [GroupId],
    [Key],
    [Value],
    [Description],
    [DataType]
FROM [dbo].[Config] 
WHERE [GroupId] = 200 AND [Key] LIKE 'EmailProcessor%'
ORDER BY [Key];

PRINT 'Email queue and processor setup completed successfully!';
GO
