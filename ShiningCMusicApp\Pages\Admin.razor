@page "/admin"
@using ShiningCMusicCommon.Models
@using ShiningCMusicCommon.Enums
@using ShiningCMusicCommon.Utilities
@using ShiningCMusicCommon.Constants
@using ShiningCMusicCommon.Extensions
@using ShiningCMusicApp.Services
@using ShiningCMusicApp.Services.Interfaces
@using ShiningCMusicApp.Components
@using ShiningCMusicApp.Components.Buttons
@using ShiningCMusicApp.Components.Dialogs
@using ShiningCMusicApp.Authorization
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Navigations
@using Microsoft.AspNetCore.Components.Authorization
@attribute [RequireLevel80Access]
@inherits AdminBase

<PageTitle>Maintenance Panel</PageTitle>

<AuthorizeView>
    <Authorized Context="authContext">
        @{
            var user = authContext.User;
        }

        <div class="container-fluid">
            <SfMediaQuery @bind-ActiveBreakpoint="activeBreakpoint" OnBreakpointChanged="OnBreakpointChanged"></SfMediaQuery>
            <div class="row">
                <div class="col-12">
                    <h1 class="mb-3 mb-md-4 fs-3 fs-md-1">⚙️ <span class="d-none d-sm-inline">Maintenance Panel</span><span class="d-sm-none">Maintenance</span></h1>

            @if (isLoading)
            {
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading maintenance data...</p>
                </div>
            }
            else
            {
                <SfTab @bind-SelectedItem="selectedTabIndex" SwipeMode="TabSwipeMode.Mouse">
                    <TabItems>
                        <!-- Users Tab -->
                        <TabItem>
                            <ChildContent>
                                <TabHeader Text="👥 Users" />
                            </ChildContent>
                            <ContentTemplate>
                                <div class="p-3">
                                    <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center mb-3">
                                        <h5 class="mb-2 mb-md-0 d-flex align-items-center">
                                            <span class="me-2">👥</span>
                                            <span>Users</span>
                                        </h5>
                                        <PageActionButtonGroup IsThirdDisabled="@(!user.HasAccessLevel(Permissions.Users.Create))"
                                                               ThirdTextDesktop="Add New User"
                                                               ThirdTextMobile="Add User"
                                                               ThirdIcon="bi bi-person-add"
                                                               OnThirdClick="OpenCreateUserModal"
                                                               FourthText="Refresh"
                                                               FourthIcon="bi bi-arrow-clockwise"
                                                               OnFourthClick="RefreshData" />
                                    </div>
                                    <SfGrid @key="@($"users-{gridKey}")" DataSource="@users" 
                                            AllowPaging="true" 
                                            AllowSorting="true" 
                                            AllowFiltering="true"
                                            AllowResizing="true"
                                            GridLines="GridLine.Both"
                                            AllowMultiSorting="false"
                                            EnableAdaptiveUI="true"
                                            AdaptiveUIMode="AdaptiveMode.Mobile"
                                            Height="400" 
                                            CssClass="mobile-grid">
                                        <GridPageSettings PageSize="8"></GridPageSettings>
                                                <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.CheckBox"></GridFilterSettings>
                                        <GridSortSettings>
                                            <GridSortColumns>
                                                <GridSortColumn Field="@nameof(User.LoginName)" Direction="SortDirection.Ascending"></GridSortColumn>
                                            </GridSortColumns>
                                        </GridSortSettings>
                                        <GridColumns>
                                            <GridColumn Field=@nameof(User.LoginName) HeaderText="Login Name" Width="100" IsPrimaryKey="true"></GridColumn>
                                            <GridColumn Field=@nameof(User.UserName) HeaderText="User Name" Width="150"></GridColumn>
                                            <GridColumn Field=@nameof(User.RoleDescription) HeaderText="Role" Width="120"></GridColumn>
                                            <GridColumn Field=@nameof(User.Note) HeaderText="Note" Width="200"></GridColumn>
                                            <GridColumn HeaderText="Actions" Width="150" AllowFiltering="false" AllowSorting="false">
                                                <Template>
                                                    @{
                                                        var user = (context as User);
                                                    }
                                                    <GridActionButtonGroup ThirdText="Edit"
                                                                           ThirdIcon="bi bi-pencil"
                                                                           IsThirdDisabled="@(!authContext.User.HasAccessLevel(Permissions.Users.Edit))"
                                                                           OnThirdClick="() => OpenEditUserModal(user)"
                                                                           FourthText="Delete"
                                                                           FourthIcon="bi bi-trash"
                                                                           IsFourthDisabled="@(!authContext.User.HasAccessLevel(Permissions.Users.Delete))"
                                                                           OnFourthClick="() => DeleteUser(user?.LoginName)"
                                                                           ShowActionButtonLabel="@ShowActionButtonLabel" />
                                                </Template>
                                            </GridColumn>
                                        </GridColumns>
                                    </SfGrid>
                                </div>
                            </ContentTemplate>
                        </TabItem>

                        <!-- User Roles Tab -->
                        <TabItem>
                            <ChildContent>
                                <TabHeader Text="🔐 User Roles" />
                            </ChildContent>
                            <ContentTemplate>
                                <div class="p-3">
                                    <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center mb-3">
                                        <h5 class="mb-2 mb-md-0 d-flex align-items-center">
                                            <span class="me-2">🔐</span>
                                            <span class="d-none d-md-inline text-nowrap">User Roles</span>
                                            <span class="d-md-none">Roles</span>
                                        </h5>
                                        <PageActionButtonGroup IsThirdDisabled="@(!user.HasAccessLevel(Permissions.Level100))"
                                                               ThirdTextDesktop="Add New Role"
                                                               ThirdTextMobile="Add Role"
                                                               OnThirdClick="OpenCreateUserRoleModal"
                                                               FourthText="Refresh"
                                                               FourthIcon="bi bi-arrow-clockwise"
                                                               OnFourthClick="RefreshData" >
                                                                <ThirdIconContent>
                                                                    <span class="position-relative d-inline-block" style="width: 1.0em; height: 1.5em;">
                                                                        <i class="bi bi-person-lock" style="color: white; font-size: 1.0em;"></i>
                                                                        <i class="bi bi-plus position-absolute" style="color: white; font-size: 0.75em; top: -0.2em; right: -0.5em;"></i>
                                                                    </span>
                                                                </ThirdIconContent>
                                        </PageActionButtonGroup>
                                    </div>
                                    <SfGrid @key="@($"userRoles-{gridKey}")" DataSource="@userRoles" 
                                            AllowPaging="true" 
                                            AllowSorting="true" 
                                            AllowFiltering="true"
                                            AllowResizing="true" 
                                            GridLines="GridLine.Both"
                                            AllowMultiSorting="false"
                                            EnableAdaptiveUI="true"
                                            AdaptiveUIMode="AdaptiveMode.Mobile"
                                            Height="400" 
                                            CssClass="mobile-grid">
                                        <GridPageSettings PageSize="8"></GridPageSettings>
                                        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.CheckBox"></GridFilterSettings>
                                        <GridSortSettings>
                                            <GridSortColumns>
                                                <GridSortColumn Field="@nameof(UserRole.ID)" Direction="SortDirection.Ascending"></GridSortColumn>
                                            </GridSortColumns>
                                        </GridSortSettings>
                                        <GridColumns>
                                            <GridColumn Field=@nameof(UserRole.ID) HeaderText="ID" Width="50" IsPrimaryKey="true"
                                                       AllowFiltering="false"></GridColumn>
                                            <GridColumn Field=@nameof(UserRole.Description) HeaderText="Description" Width=@(columnWidth)></GridColumn>
                                            <GridColumn Field=@nameof(UserRole.AccessLevel) HeaderText="Access Level" Width="100" 
                                                AllowFiltering="true" 
                                                TextAlign="TextAlign.Center">
                                                <Template>
                                                    @{
                                                        var role = (context as UserRole);
                                                        var accessLevel = role?.AccessLevel ?? 0;
                                                        var fillPercentage = accessLevel; // 0-100
                                                    }
                                                    <div class="d-flex align-items-center justify-content-center">
                                                        <div class="access-level-icon-grid" title="Access Level: @accessLevel">
                                                            <svg width="40" height="40" viewBox="0 0 40 40" class="access-level-svg-grid">
                                                                <!-- Background circle -->
                                                                <circle cx="20" cy="20" r="16" fill="none" stroke="#e0e0e0" stroke-width="3"/>
                                                                <!-- Fill circle based on access level -->
                                                                <circle cx="20" cy="20" r="16" fill="none"
                                                                        stroke="@GetAccessLevelColor(accessLevel)"
                                                                        stroke-width="3"
                                                                        stroke-dasharray="@($"{100.5 * fillPercentage / 100} 100.5")"
                                                                        stroke-dashoffset="0"
                                                                        transform="rotate(-90 20 20)"
                                                                        class="access-level-fill"/>
                                                                <!-- Center text -->
                                                                <text x="20" y="25" text-anchor="middle" class="access-level-text-grid">@accessLevel</text>
                                                            </svg>
                                                        </div>
                                                    </div>
                                                </Template>
                                            </GridColumn>
                                            <GridColumn HeaderText="Actions" Width="150" AllowFiltering="false" AllowSorting="false">
                                                <Template>
                                                    @{
                                                        var role = (context as UserRole);
                                                    }
                                                    <GridActionButtonGroup ThirdText="Edit"
                                                                           ThirdIcon="bi bi-pencil"
                                                                           IsThirdDisabled="@(!authContext.User.HasAccessLevel(Permissions.Level100))"
                                                                           OnThirdClick="() => OpenEditUserRoleModal(role)"
                                                                           FourthText="Delete"
                                                                           FourthIcon="bi bi-trash"
                                                                           IsFourthDisabled="@(!authContext.User.HasAccessLevel(Permissions.Level100))"
                                                                           OnFourthClick="() => DeleteUserRole(role?.ID)"
                                                                           ShowActionButtonLabel="@ShowActionButtonLabel" />
                                                </Template>
                                            </GridColumn>
                                        </GridColumns>
                                    </SfGrid>
                                </div>
                            </ContentTemplate>
                        </TabItem>

                        <!-- Subjects Tab -->
                        <TabItem>
                            <ChildContent>
                                <TabHeader Text="📚 Subjects" />
                            </ChildContent>
                            <ContentTemplate>
                                <div class="p-3">
                                    <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center mb-3">
                                        <h5 class="mb-2 mb-md-0 d-flex align-items-center">
                                            <span class="me-2">📚</span>
                                            <span>Subjects</span>
                                        </h5>
                                        <PageActionButtonGroup IsThirdDisabled="@(!user.HasAccessLevel(Permissions.Level80))"
                                                               ThirdTextDesktop="Add New Subject"
                                                               ThirdTextMobile="Add Subject"
                                                               ThirdIcon="bi bi-journal-plus"
                                                               OnThirdClick="OpenCreateSubjectModal"
                                                               FourthText="Refresh"
                                                               FourthIcon="bi bi-arrow-clockwise"
                                                               OnFourthClick="RefreshData" />
                                    </div>
                                    <SfGrid @key="@($"subjects-{gridKey}")" DataSource="@subjects" 
                                            AllowPaging="true" 
                                            AllowSorting="true" 
                                            AllowFiltering="true"
                                            AllowResizing="true" 
                                            GridLines="GridLine.Both"
                                            AllowMultiSorting="false"
                                            EnableAdaptiveUI="true"
                                            AdaptiveUIMode="AdaptiveMode.Mobile"
                                            Height="400" 
                                            CssClass="mobile-grid">
                                        <GridPageSettings PageSize="8"></GridPageSettings>
                                        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.CheckBox"></GridFilterSettings>
                                        <GridSortSettings>
                                            <GridSortColumns>
                                                <GridSortColumn Field="@nameof(Subject.SubjectId)" Direction="SortDirection.Ascending"></GridSortColumn>
                                            </GridSortColumns>
                                        </GridSortSettings>
                                        <GridColumns>
                                            <GridColumn Field=@nameof(Subject.SubjectId) HeaderText="ID" Width="50" IsPrimaryKey="true"
                                                       AllowFiltering="false"></GridColumn>
                                            <GridColumn Field=@nameof(Subject.SubjectName) HeaderText="Subject Name" Width=@(columnWidth)></GridColumn>
                                            <GridColumn HeaderText="Actions" Width="150" AllowFiltering="false" AllowSorting="false">
                                                <Template>
                                                    @{
                                                        var subject = (context as Subject);
                                                    }
                                                    <GridActionButtonGroup ThirdText="Edit"
                                                                           ThirdIcon="bi bi-pencil"
                                                                           IsThirdDisabled="@(!authContext.User.HasAccessLevel(Permissions.Level80))"
                                                                           OnThirdClick="() => OpenEditSubjectModal(subject)"
                                                                           FourthText="Delete"
                                                                           FourthIcon="bi bi-trash"
                                                                           IsFourthDisabled="@(!authContext.User.HasAccessLevel(Permissions.Level80))"
                                                                           OnFourthClick="() => DeleteSubject(subject)"
                                                                           ShowActionButtonLabel="@ShowActionButtonLabel" />
                                                </Template>
                                            </GridColumn>
                                        </GridColumns>
                                    </SfGrid>
                                </div>
                            </ContentTemplate>
                        </TabItem>

                        <!-- Locations Tab -->
                        <TabItem>
                            <ChildContent>
                                <TabHeader Text="📍 Locations" />
                            </ChildContent>
                            <ContentTemplate>
                                <div class="p-3">
                                    <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center mb-3">
                                        <h5 class="mb-2 mb-md-0 d-flex align-items-center">
                                            <span class="me-2">📍</span>
                                            <span>Locations</span>
                                        </h5>
                                        <PageActionButtonGroup IsThirdDisabled="@(!user.HasAccessLevel(Permissions.Level80))"
                                                               ThirdTextDesktop="Add New Location"
                                                               ThirdTextMobile="Add Location"
                                                               OnThirdClick="OpenCreateLocationModal"
                                                               FourthText="Refresh"
                                                               FourthIcon="bi bi-arrow-clockwise"
                                                               OnFourthClick="RefreshData" >
                                                                <ThirdIconContent>
                                                                    <span class="position-relative d-inline-block" style="width: 1.0em; height: 1.5em;">
                                                                        <i class="bi bi-geo" style="color: white; font-size: 1.0em;"></i>
                                                                        <i class="bi bi-plus position-absolute" style="color: white; font-size: 0.75em; top: -0.2em; right: -0.5em;"></i>
                                                                    </span>
                                                                </ThirdIconContent>
                                        </PageActionButtonGroup>
                                    </div>
                                    <SfGrid @key="@($"locations-{gridKey}")" DataSource="@locations" 
                                            AllowPaging="true" 
                                            AllowSorting="true" 
                                            AllowFiltering="true"
                                            AllowResizing="true" 
                                            GridLines="GridLine.Both"
                                            AllowMultiSorting="false"
                                            EnableAdaptiveUI="true"
                                            AdaptiveUIMode="AdaptiveMode.Mobile"
                                            Height="400" 
                                            CssClass="mobile-grid">
                                        <GridPageSettings PageSize="8"></GridPageSettings>
                                        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.CheckBox"></GridFilterSettings>
                                        <GridSortSettings>
                                            <GridSortColumns>
                                                <GridSortColumn Field="@nameof(Location.LocationId)" Direction="SortDirection.Ascending"></GridSortColumn>
                                            </GridSortColumns>
                                        </GridSortSettings>
                                        <GridColumns>
                                            <GridColumn Field=@nameof(Location.LocationId) HeaderText="ID" Width="50" IsPrimaryKey="true"
                                                       AllowFiltering="false"></GridColumn>
                                            <GridColumn Field=@nameof(Location.LocationName) HeaderText="Location Name" Width=@(columnWidth)></GridColumn>
                                            <GridColumn HeaderText="Actions" Width="150" AllowFiltering="false" AllowSorting="false">
                                                <Template>
                                                    @{
                                                        var location = (context as Location);
                                                    }
                                                    <GridActionButtonGroup ThirdText="Edit"
                                                                           ThirdIcon="bi bi-pencil"
                                                                           IsThirdDisabled="@(!authContext.User.HasAccessLevel(Permissions.Level80))"
                                                                           OnThirdClick="() => OpenEditLocationModal(location)"
                                                                           FourthText="Delete"
                                                                           FourthIcon="bi bi-trash"
                                                                           IsFourthDisabled="@(!authContext.User.HasAccessLevel(Permissions.Level80))"
                                                                           OnFourthClick="() => DeleteLocation(location)"
                                                                           ShowActionButtonLabel="@ShowActionButtonLabel" />
                                                </Template>
                                            </GridColumn>
                                        </GridColumns>
                                    </SfGrid>
                                </div>
                            </ContentTemplate>
                        </TabItem>
                    </TabItems>
                </SfTab>
            }
        </div>
    </div>
</div>

<!-- User Modal -->
<SfDialog @bind-Visible="showUserModal" Header="@userModalTitle" Width="500px" Height="auto" IsModal="true"
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            <EditForm Model="@currentUser" OnValidSubmit="@SaveUser">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-danger" />

                <div class="mb-3">
                    <label class="form-label">Login Name <span class="text-danger">*</span></label>
                    <SfTextBox @bind-Value="currentUser.LoginName" Placeholder="Enter login name"
                               CssClass="form-control" Enabled="@(!isEditUserMode)"></SfTextBox>
                    <ValidationMessage For="@(() => currentUser.LoginName)" />
                </div>

                <div class="mb-3">
                    <label class="form-label">User Name</label>
                    <SfTextBox @bind-Value="currentUser.UserName" Placeholder="Enter user name"
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentUser.UserName)" />
                </div>

                <div class="mb-3">
                    <label class="form-label">Password</label>
                    <SfTextBox @bind-Value="currentUser.Password" Type="InputType.Password"
                               Placeholder="@GetPasswordPlaceholder()" CssClass="form-control"
                               @onblur="ValidatePassword"></SfTextBox>
                    <ValidationMessage For="@(() => currentUser.Password)" />
                    @if (passwordValidationErrors.Any())
                    {
                        <div class="password-validation text-danger">
                            @foreach (var error in passwordValidationErrors)
                            {
                                <small>@error</small>
                            }
                        </div>
                    }
                    @if (!string.IsNullOrEmpty(currentUser.Password) && passwordValidationErrors.Count == 0)
                    {
                        <div class="password-validation text-success">
                            <small>✓ Password meets requirements</small>
                        </div>
                    }
                </div>

                <div class="mb-3">
                    <label class="form-label">Confirm Password</label>
                    <SfTextBox @bind-Value="confirmPassword" Type="InputType.Password"
                               Placeholder="Confirm your password" CssClass="form-control"
                               @onblur="ValidatePasswordMatch"></SfTextBox>
                    @if (!string.IsNullOrEmpty(passwordMatchError))
                    {
                        <div class="password-validation text-danger">
                            <small>@passwordMatchError</small>
                        </div>
                    }
                    @if (!string.IsNullOrEmpty(confirmPassword) && string.IsNullOrEmpty(passwordMatchError))
                    {
                        <div class="password-validation text-success">
                            <small>✓ Passwords match</small>
                        </div>
                    }
                </div>

                <div class="mb-3">
                    <label class="form-label">Role</label>
                    <SfDropDownList TValue="int?" TItem="UserRole" @bind-Value="currentUser.RoleId"
                                    DataSource="@userRoles" CssClass="form-control" Placeholder="Select a role">
                        <DropDownListFieldSettings Value="ID" Text="Description"></DropDownListFieldSettings>
                        <DropDownListEvents TValue="int?" TItem="UserRole" ValueChange="@OnRoleChanged"></DropDownListEvents>
                    </SfDropDownList>
                    <ValidationMessage For="@(() => currentUser.RoleId)" />
                </div>

                @if (!isEditUserMode && showAssignmentSection)
                {
                    <div class="mb-3">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">👤 Assign to Person</h6>
                                <small class="text-muted">Link this user account to an existing tutor or student</small>
                            </div>
                            <div class="card-body">
                                @if (GetSelectedRoleDescription() == UserRoleEnum.Tutor.ToString())
                                {
                                    <div class="mb-3">
                                        <label class="form-label">Select Tutor</label>
                                        <SfDropDownList TValue="int?" TItem="Tutor" @bind-Value="selectedTutorId"
                                                        DataSource="@GetAvailableTutors()" CssClass="form-control"
                                                        Placeholder="Select a tutor to link">
                                            <DropDownListFieldSettings Value="TutorId" Text="TutorName"></DropDownListFieldSettings>
                                        </SfDropDownList>
                                        <small class="form-text text-muted">This will update the tutor's login name to match this user</small>
                                    </div>
                                }
                                else if (GetSelectedRoleDescription() == UserRoleEnum.Student.ToString())
                                {
                                    <div class="mb-3">
                                        <label class="form-label">Select Student</label>
                                        <SfDropDownList TValue="int?" TItem="Student" @bind-Value="selectedStudentId"
                                                        DataSource="@GetAvailableStudents()" CssClass="form-control"
                                                        Placeholder="Select a student to link">
                                            <DropDownListFieldSettings Value="StudentId" Text="StudentName"></DropDownListFieldSettings>
                                        </SfDropDownList>
                                        <small class="form-text text-muted">This will update the student's login name to match this user</small>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                }

                <div class="mb-3">
                    <label class="form-label">Note</label>
                    <SfTextBox @bind-Value="currentUser.Note" Placeholder="Enter note"
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentUser.Note)" />
                </div>
                <ModalActionButtonGroup FirstText="@(isEditUserMode ? "Update" : "Create")"
                                        FirstIcon="@(isEditUserMode ? "bi bi-pencil" : "bi bi-plus-circle")"
                                        FirstButtonType="submit"
                                        IsLoading="@isSaving"
                                        IsFirstDisabled="@isSaving"
                                        SecondText="Cancel"
                                        OnSecondClick="CloseUserModal" />
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- UserRole Modal -->
<SfDialog @bind-Visible="showUserRoleModal" Header="@userRoleModalTitle" Width="450px" Height="auto" IsModal="true"
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            <EditForm Model="@currentUserRole" OnValidSubmit="@SaveUserRole">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-danger" />

                <div class="mb-3">
                    <label class="form-label">ID <span class="text-danger">*</span></label>
                    <SfNumericTextBox TValue="int" @bind-Value="currentUserRole.ID"
                                      Placeholder="Enter role ID" CssClass="form-control"
                                      Readonly="@isEditUserRoleMode" Min="1" Max="999"
                                      ShowSpinButton="false" Format="0"></SfNumericTextBox>
                    <ValidationMessage For="@(() => currentUserRole.ID)" />
                    @if (!isEditUserRoleMode)
                    {
                        <small class="text-muted">Choose a unique ID number for this role (suggested: @currentUserRole.ID)</small>
                    }
                    else
                    {
                        <small class="text-muted">Role ID cannot be changed when editing</small>
                    }
                </div>

                <div class="mb-3">
                    <label class="form-label">Description <span class="text-danger">*</span></label>
                    <SfTextBox @bind-Value="currentUserRole.Description" Placeholder="Enter role description"
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentUserRole.Description)" />
                </div>

                <div class="mb-3">
                    <label class="form-label">Access Level <span class="text-danger">*</span></label>
                    <SfNumericTextBox TValue="int" @bind-Value="currentUserRole.AccessLevel"
                                      Placeholder="Enter access level (1-100)" CssClass="form-control"
                                      Min="1" Max="100" ShowSpinButton="true" Format="0"></SfNumericTextBox>
                    <ValidationMessage For="@(() => currentUserRole.AccessLevel)" />
                    <small class="text-muted">Access level from 1 to 100 (Student=10, Tutor=20, Manager=80, Admin=100)</small>
                </div>
                <ModalActionButtonGroup FirstText="@(isEditUserRoleMode ? "Update" : "Create")"
                                        FirstIcon="@(isEditUserRoleMode ? "bi bi-pencil" : "bi bi-plus-circle")"
                                        FirstButtonType="submit"
                                        IsLoading="@isSaving"
                                        IsFirstDisabled="@isSaving"
                                        SecondText="Cancel"
                                        OnSecondClick="CloseUserRoleModal" />
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Subject Modal -->
<SfDialog @bind-Visible="showSubjectModal" Header="@subjectModalTitle" Width="400px" Height="auto" IsModal="true" 
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            <EditForm Model="@currentSubject" OnValidSubmit="@SaveSubject">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-danger" />
                
                <div class="mb-3">
                    <label class="form-label">Subject Name <span class="text-danger">*</span></label>
                    <SfTextBox @bind-Value="currentSubject.SubjectName" Placeholder="Enter subject name" 
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentSubject.SubjectName)" />
                </div>
                <ModalActionButtonGroup FirstText="@(isEditSubjectMode ? "Update" : "Create")"
                                        FirstIcon="@(isEditSubjectMode ? "bi bi-pencil" : "bi bi-plus-circle")"
                                        FirstButtonType="submit"
                                        IsLoading="@isSaving"
                                        IsFirstDisabled="@isSaving"
                                        SecondText="Cancel"
                                        OnSecondClick="CloseSubjectModal" />
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Location Modal -->
<SfDialog @bind-Visible="showLocationModal" Header="@locationModalTitle" Width="400px" Height="auto" IsModal="true" 
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            <EditForm Model="@currentLocation" OnValidSubmit="@SaveLocation">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-danger" />
                
                <div class="mb-3">
                    <label class="form-label">Location Name <span class="text-danger">*</span></label>
                    <SfTextBox @bind-Value="currentLocation.LocationName" Placeholder="Enter location name" 
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentLocation.LocationName)" />
                </div>
                <ModalActionButtonGroup FirstText="@(isEditLocationMode ? "Update" : "Create")"
                                        FirstIcon="@(isEditLocationMode ? "bi bi-pencil" : "bi bi-plus-circle")"
                                        FirstButtonType="submit"
                                        IsLoading="@isSaving"
                                        IsFirstDisabled="@isSaving"
                                        SecondText="Cancel"
                                        OnSecondClick="CloseLocationModal" />
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Delete Confirmation Dialog -->
<DeleteConfirmationDialog />

<!-- Alert Dialog -->
<AlertDialog />

    </Authorized>
</AuthorizeView>