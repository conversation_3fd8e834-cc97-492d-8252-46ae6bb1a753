@page "/students"
@using ShiningCMusicCommon.Models
@using ShiningCMusicCommon.Enums
@using ShiningCMusicApp.Services
@using ShiningCMusicApp.Services.Interfaces
@using ShiningCMusicApp.Components
@using ShiningCMusicApp.Authorization
@using ShiningCMusicCommon.Constants
@using ShiningCMusicApp.Components.Email
@using ShiningCMusicApp.Components.Buttons
@using ShiningCMusicApp.Components.Dialogs
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.DropDowns
@attribute [RequireLevel80Access]
@inherits StudentsBase

<PageTitle>Student Management</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-3 mb-md-4 fs-3 fs-md-1">👨‍🎓 <span class="d-none d-sm-inline">Student Management</span><span class="d-sm-none">Students</span></h1>
            
            @if (isLoading)
            {
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading students...</p>
                </div>
            }
            else
            {
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                            <div>
                                <h5 class="mb-1">Students</h5>
                                @if (selectedStudents.Any())
                                {
                                    <small class="text-muted">@selectedStudents.Count student(s) selected</small>
                                }
                            </div>
                            <PageActionButtonGroup ShowFirstButton="@(selectedStudents.Any())"
                                                   FirstTextDesktop="@($"Send Email to Selected ({selectedStudents.Count})")"
                                                   FirstTextMobile="@($"Email ({selectedStudents.Count})")"
                                                   FirstIcon="bi bi-envelope"
                                                   OnFirstClick="ShowBulkEmailModal"
                                                   ShowSecondButton="@(selectedStudents.Any())"
                                                   SecondText="Clear Selection"
                                                   SecondIcon="bi bi-x-circle"
                                                   SecondButtonClass="btn-outline-danger"
                                                   SecondIconColor="inherit"
                                                   OnSecondClick="ClearSelection"
                                                   ThirdTextDesktop="Add New Student"
                                                   ThirdTextMobile="Add Student"
                                                   ThirdIcon="bi bi-person-add"
                                                   OnThirdClick="OpenCreateModal"
                                                   FourthText="Refresh"
                                                   FourthIcon="bi bi-arrow-clockwise"
                                                   OnFourthClick="RefreshData" />
                        </div>
                    </div>
                    <div class="card-body p-1 p-md-3">
                        <SfGrid @ref="studentsGrid"
                                DataSource="@students"
                                AllowPaging="true"
                                AllowSorting="true"
                                AllowFiltering="true"
                                AllowResizing="true"
                                AllowMultiSorting="false"
                                AllowSelection="true"
                                EnableAdaptiveUI="true"
                                AdaptiveUIMode="AdaptiveMode.Mobile"
                                GridLines="GridLine.Both"
                                Height="900"
                                CssClass="mobile-grid">
                            <GridSelectionSettings Type="Syncfusion.Blazor.Grids.SelectionType.Multiple" CheckboxOnly="true"></GridSelectionSettings>
                            <GridEvents TValue="Student"
                                RowSelected="OnRowSelected"
                                RowDeselected="OnRowDeselected">
                            </GridEvents>
                            <GridPageSettings PageSize="20"></GridPageSettings>
                            <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.CheckBox"></GridFilterSettings>
                            <GridSortSettings>
                                <GridSortColumns>
                                    <GridSortColumn Field="@nameof(Student.CreatedUTC)" Direction="SortDirection.Descending"></GridSortColumn>
                                </GridSortColumns>
                            </GridSortSettings>
                            <GridColumns>
                                <GridColumn Type="ColumnType.CheckBox" Width="50" AllowFiltering="false" AllowSorting="false"></GridColumn>
                                <GridColumn Field=@nameof(Student.StudentId) HeaderText="ID" Width="50" IsPrimaryKey="true"
                                           AllowFiltering="false"></GridColumn>
                                <GridColumn Field=@nameof(Student.StudentName) HeaderText="Name" Width="150"></GridColumn>
                                <GridColumn Field=@nameof(Student.Email) HeaderText="Email" Width="200"></GridColumn>
                                <GridColumn Field=@nameof(Student.TutorName) HeaderText="Tutor" Width="150"></GridColumn>
                                <GridColumn Field=@nameof(Student.SubjectName) HeaderText="Subject" Width="100"></GridColumn>
                                @* <GridColumn Field=@nameof(Student.LoginName) HeaderText="Login Name" Width="100"></GridColumn> *@
                                <GridColumn Field=@nameof(Student.RemainingLessons) HeaderText="Remaining Lessons" Width="100" 
                                    AllowFiltering="false" 
                                    AllowSorting="true" 
                                    TextAlign="TextAlign.Center">
                                    <Template>
                                        @{
                                            var student = (context as Student);
                                            var remainingLessons = student?.RemainingLessons ?? 0;
                                        }
                                        <span class="remaining-lessons-badge @(GetRemainingLessonsBadgeClass(remainingLessons))">
                                            @remainingLessons
                                        </span>
                                    </Template>
                                </GridColumn>
                                @* <GridColumn Field=@nameof(Student.ExcludeEmail) HeaderText="Email Reminders" Width="100" AllowFiltering="false">
                                    <Template>
                                        @{
                                            var student = (context as Student);
                                        }
                                        <span class="email-reminder-badge @(student?.ExcludeEmail == true ? "email-reminder-disabled" : "email-reminder-enabled")">
                                            @(student?.ExcludeEmail == true ? "Disabled" : "Enabled")
                                        </span>
                                    </Template>
                                </GridColumn> *@
                                <GridColumn Field=@nameof(Student.CreatedUTC) HeaderText="Created" Width="100" Format="dd/MM/yyyy"
                                           AllowFiltering="false"></GridColumn>
                                <GridColumn HeaderText="Actions" Width="200" AllowFiltering="false" AllowSorting="false">
                                    <Template>
                                        @{
                                            var student = (context as Student);
                                        }
                                        <GridActionButtonGroup ShowFirstButton="true"
                                                               FirstText="Email"
                                                               FirstIcon="bi bi-envelope"
                                                               FirstTitle="Send Payment Reminder Email"
                                                               IsFirstDisabled="@IsEmailButtonDisabled(student)"
                                                               OnFirstClick="() => SendPaymentReminderEmail(student, paymentReminderTemplate)"
                                                               ThirdText="Edit"
                                                               ThirdIcon="bi bi-pencil"
                                                               ThirdTitle="Edit Student"
                                                               OnThirdClick="() => OpenEditModal(student)"
                                                               FourthText="Delete"
                                                               FourthIcon="bi bi-trash"
                                                               FourthTitle="Delete Student"
                                                               OnFourthClick="() => DeleteStudent(student)"
                                                               ShowActionButtonLabel="@ShowActionButtonLabel"
                                                               ButtonBaseClass="grid-action-btn grid-btn-third" />
                                    </Template>
                                </GridColumn>
                            </GridColumns>
                        </SfGrid>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- Create/Edit Modal -->
<SfDialog @bind-Visible="showModal" Header="@modalTitle" Width="500px" Height="auto" IsModal="true" 
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            <EditForm Model="@currentStudent" OnValidSubmit="@SaveStudent">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-danger" />
                
                <div class="mb-3">
                    <label class="form-label">Student Name <span class="text-danger">*</span></label>
                    <SfTextBox @bind-Value="currentStudent.StudentName" Placeholder="Enter student name" 
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentStudent.StudentName)" />
                </div>

                <div class="mb-3">
                    <label class="form-label">Email</label>
                    <SfTextBox @bind-Value="currentStudent.Email" Placeholder="Enter email address"
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentStudent.Email)" />
                </div>

                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" @bind="currentStudent.ExcludeEmail" id="excludeEmailCheck">
                        <label class="form-check-label" for="excludeEmailCheck">
                            <i class="bi bi-envelope-slash me-2"></i>Exclude from payment reminder emails
                        </label>
                    </div>
                    <small class="form-text text-muted">
                        When checked, this student will not receive automated payment reminder emails when they have few lessons remaining.
                    </small>
                </div>

                <div class="mb-3">
                    <label class="form-label">Subject @if (!isEditMode) { <span class="text-danger">*</span> }</label>
                    @if (isEditMode)
                    {
                        <SfTextBox @bind-Value="currentStudentSubjectName" Readonly="true"
                                   CssClass="form-control" Placeholder="No subject assigned"></SfTextBox>
                        <small class="form-text text-muted">Subject cannot be changed when editing. Create a new student to assign a different subject.</small>
                    }
                    else
                    {
                        <SfDropDownList TValue="int?" TItem="Subject" @bind-Value="currentStudent.SubjectId"
                                        DataSource="@subjects" CssClass="form-control" Placeholder="Select a subject">
                            <DropDownListFieldSettings Value="SubjectId" Text="SubjectName"></DropDownListFieldSettings>
                            <DropDownListTemplates TItem="Subject">
                                <ItemTemplate Context="subjectItem">
                                    <span>@((subjectItem as Subject)?.SubjectName)</span>
                                </ItemTemplate>
                            </DropDownListTemplates>
                        </SfDropDownList>
                        <small class="form-text text-muted">Select a subject for this student (required)</small>
                        @if (showSubjectValidation)
                        {
                            <div class="text-danger">Subject is required.</div>
                        }
                    }
                </div>

                <div class="mb-3">
                    <label class="form-label">Assigned Tutor @if (!isEditMode) { <span class="text-danger">*</span> }</label>
                    @if (isEditMode)
                    {
                        <SfTextBox @bind-Value="currentStudentTutorName" Readonly="true"
                                   CssClass="form-control" Placeholder="No tutor assigned"></SfTextBox>
                        <small class="form-text text-muted">Tutor cannot be changed when editing. Create a new student to assign a different tutor.</small>
                    }
                    else
                    {
                        <SfDropDownList TValue="int?" TItem="Tutor" @bind-Value="currentStudent.TutorID"
                                        DataSource="@tutors" CssClass="form-control" Placeholder="Select a tutor">
                            <DropDownListFieldSettings Value="TutorId" Text="TutorName"></DropDownListFieldSettings>
                            <DropDownListTemplates TItem="Tutor">
                                <ItemTemplate Context="tutorItem">
                                    <span>@((tutorItem as Tutor)?.TutorName)</span>
                                </ItemTemplate>
                            </DropDownListTemplates>
                        </SfDropDownList>
                        <small class="form-text text-muted">Select a tutor to assign to this student (required)</small>
                        @if (showTutorValidation)
                        {
                            <div class="text-danger">Tutor is required.</div>
                        }
                    }
                </div>
                <div class="mb-3">
                    <label class="form-label">Login Name</label>
                    <SfTextBox @bind-Value="currentStudent.LoginName" Placeholder="Assigned via user management"
                               CssClass="form-control" Readonly="true"></SfTextBox>
                    <small class="form-text text-muted">Login name is managed through the Admin page user assignment</small>
                </div>
                <ModalActionButtonGroup FirstText="@(isEditMode ? "Update" : "Create")"
                                        FirstIcon="@(isEditMode ? "bi bi-pencil" : "bi bi-plus-circle")"
                                        FirstButtonType="submit"
                                        IsLoading="@isSaving"
                                        IsFirstDisabled="@isSaving"
                                        SecondText="Cancel"
                                        OnSecondClick="CloseModal" />
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Template Selection Modal -->
<EmailTemplateSelectionModal @bind-IsVisible="showTemplateSelectionModal"
                             Title="@templateSelectionModalTitle"
                             EmailTemplates="@emailTemplates"
                             SelectedTemplateName="@selectedTemplateName"
                             IsLoading="@isSendingEmail"
                             OnTemplateSelected="@SendSelectedTemplate"
                             OnCancel="@CloseTemplateSelectionModal" />

<!-- Reusable Bulk Email Modal>
<BulkEmailModal @bind-IsVisible="showBulkEmailModal"
                TItem="Student"
                EmailTemplates="@emailTemplates"
                Recipients="@selectedStudents"
                RecipientBuilder="@BuildStudentRecipient"
                DisplayNameSelector="@(s => s.StudentName ?? "Unknown")"
                EmailSelector="@(s => s.Email ?? "")"
                SubjectSelector="@(s => s.SubjectName)"
                AdditionalDisplayFields="@GetStudentDisplayFields()"
                EntityDisplayName="Students"
                OnEmailSent="@OnBulkEmailComplete"
                OnClearSelection="@ClearSelection" />

<!-- Delete Confirmation Dialog -->
<DeleteConfirmationDialog />

<!-- Alert Dialog -->
<AlertDialog />
