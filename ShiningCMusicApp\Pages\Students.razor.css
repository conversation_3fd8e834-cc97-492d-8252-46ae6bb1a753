/* Students page specific styles */

.email-reminder-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
}

.email-reminder-enabled {
    background-color: #198754;
    color: white;
}

.email-reminder-disabled {
    background-color: #dc3545;
    color: white;
}

.remaining-lessons-badge {
    display: inline-flex; /* allows perfect centering */
    align-items: center; /* vertical center */
    justify-content: center; /* horizontal center */
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 50%; /* make it round */
    min-width: 2rem; /* ensures same size for 1 or 2 digits */
    height: 2rem; /* match height to width */
    padding: 0; /* no extra padding, size fixed by min-width/height */
    line-height: 1; /* avoids vertical misalignment */
    text-align: center;
    box-sizing: border-box;
}

/* Mobile - perfect circle */
@media (max-width: 768px) {
    .remaining-lessons-badge {
        width: 2rem !important; /* equal width */
        height: 2rem !important; /* equal height */
    }
}

.remaining-lessons-normal {
    background-color: #28a745; /* green */
    color: white;
}

.remaining-lessons-low {
    background-color: #ffc107; /* yellow */
    color: black; /* Better contrast for yellow */
}

.remaining-lessons-zero {
    background-color: #dc3545; /* red */
    color: white;
}

.form-check-label {
    font-weight: 500;
    margin-bottom: 0;
}

.form-check-input:checked {
    background-color: #dc3545;
    border-color: #dc3545;
}

.form-check-input:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}

/* Responsive adjustments for mobile */
@media (max-width: 768px) {
    .email-reminder-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }

    .remaining-lessons-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
        min-width: 1.5rem;
    }
}
