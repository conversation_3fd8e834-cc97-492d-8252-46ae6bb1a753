using Microsoft.EntityFrameworkCore;
using OpenIddict.Validation.AspNetCore;
using ShiningCMusicApi.Infrastructure;
using ShiningCMusicApi.Services;
using ShiningCMusicApi.Services.Implementations;
using ShiningCMusicApi.Services.Interfaces;

using System.Security.Cryptography.X509Certificates;

var builder = WebApplication.CreateBuilder(args);

// Add logging
builder.Services.AddLogging(logging =>
{
    logging.AddConsole();
    logging.AddDebug();
});

// Add services to the container.

// Add connection string
builder.Services.AddSingleton<IConfiguration>(builder.Configuration);

// Add our services
builder.Services.AddScoped<ILessonService, LessonService>();
builder.Services.AddScoped<ITutorService, TutorService>();
builder.Services.AddScoped<IStudentService, StudentService>();
builder.Services.AddScoped<ISubjectService, SubjectService>();
builder.Services.AddScoped<ILocationService, LocationService>();
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddScoped<IPasswordService, PasswordService>();
builder.Services.AddScoped<IEmailTemplateService, EmailTemplateService>();
builder.Services.AddScoped<IEmailService, EmailService>();
builder.Services.AddScoped<IEmailQueueService, EmailQueueService>();
builder.Services.AddScoped<ITimesheetService, TimesheetService>();
builder.Services.AddScoped<IConfigService, ConfigService>();

// Add background services
builder.Services.AddHostedService<ShiningCMusicApi.Services.BackgroundServices.LessonCleanupService>();
builder.Services.AddHostedService<ShiningCMusicApi.Services.BackgroundServices.TutorCleanupService>();
builder.Services.AddHostedService<ShiningCMusicApi.Services.BackgroundServices.StudentCleanupService>();
builder.Services.AddHostedService<ShiningCMusicApi.Services.BackgroundServices.TimesheetCleanupService>();
builder.Services.AddHostedService<ShiningCMusicApi.Services.BackgroundServices.PaymentReminderService>();
builder.Services.AddHostedService<ShiningCMusicApi.Services.BackgroundServices.EmailProcessorService>();

// Add OpenIddict seeder
builder.Services.AddHostedService<OpenIddictSeeder>();

// Add ApplicationDbContext for OpenIddict (includes plain text secrets table)
builder.Services.AddDbContext<ApplicationDbContext>(options =>
{
    options.UseSqlServer(Environment.GetEnvironmentVariable("DATABASE_CONNECTION_STRING") ?? builder.Configuration.GetConnectionString("MusicSchool"));
    options.UseOpenIddict();
});

// Register services
builder.Services.AddScoped<IClientSecretService, ClientSecretService>();
builder.Services.AddScoped<CustomTokenLifetimeHandler>();

// AuthConfig removed - no longer needed without IdentityServer4

// Configure OpenIddict
builder.Services.AddOpenIddict()
    .AddCore(options =>
    {
        options.UseEntityFrameworkCore()
               .UseDbContext<ApplicationDbContext>();
    })
    .AddServer(options =>
    {
        // Enable the token and discovery endpoints
        options.SetTokenEndpointUris("/connect/token");
        options.SetConfigurationEndpointUris("/.well-known/openid_configuration");

        // Configure token lifetimes (ADD THIS)
        options.SetAccessTokenLifetime(TimeSpan.FromHours(2)); // 2 hours

        // Enable the client credentials flow
        options.AllowClientCredentialsFlow();

        // Disable encryption so tokens are plain JWT
        options.DisableAccessTokenEncryption();

        X509Certificate2 cert;
        bool isAzure = !string.IsNullOrEmpty(Environment.GetEnvironmentVariable("WEBSITE_INSTANCE_ID"));

        if (isAzure)
        {
            // Load Base64 PFX from Azure App Settings
            string base64 = Environment.GetEnvironmentVariable("SIGNING_CERT_BASE64") ?? string.Empty;
            string password = Environment.GetEnvironmentVariable("SIGNING_CERT_PASSWORD") ?? string.Empty;

            if (string.IsNullOrEmpty(base64) || string.IsNullOrEmpty(password))
                throw new Exception("Base64 PFX or password not found.");

            cert = LoadCertificateFromBase64(base64, password);
        }
        else
        {
            // Load certificate config
            string certName = builder.Configuration["Certificate:Name"]
                            ?? "identity.pfx";
            string certPassword = builder.Configuration["Certificate:Password"]
                            ?? string.Empty;
            string certFile = Path.Combine(builder.Environment.ContentRootPath, certName);
            cert = LoadCertificate(certFile, certPassword);
        }

        options.AddSigningCertificate(cert);
        options.AddEncryptionCertificate(cert);

        // Register the ASP.NET Core host and configure the ASP.NET Core options
        options.UseAspNetCore()
               .DisableTransportSecurityRequirement();

        // Add custom event handler for token requests
        options.AddEventHandler<OpenIddict.Server.OpenIddictServerEvents.HandleTokenRequestContext>(builder =>
            builder.UseScopedHandler<CustomTokenLifetimeHandler>());
    })
    .AddValidation(options =>
    {
        // Import the configuration from the local OpenIddict server instance
        options.UseLocalServer();

        // Register the ASP.NET Core host
        options.UseAspNetCore();
    });

var apiBaseUrl = Environment.GetEnvironmentVariable("API_BASE_URL") ?? builder.Configuration["ApiBaseUrl"];

// Add Authentication - OpenIddict only
builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme;
});

// Add CORS for Blazor client
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowBlazorApp", policy =>
    {
            policy.AllowAnyOrigin()
                  .AllowAnyHeader()
                  .AllowAnyMethod();
    });
});

// Add Authorization policies
builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("AdminOnly", policy => policy.RequireRole("Administrator"));
    options.AddPolicy("TutorOrAdmin", policy => policy.RequireRole("Tutor", "Administrator"));
    options.AddPolicy("StudentOrAdmin", policy => policy.RequireRole("Student", "Administrator"));
    options.AddPolicy("AuthenticatedUser", policy => policy.RequireAuthenticatedUser());
});

builder.Services.AddControllers();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();
builder.Services.AddMemoryCache();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseCors("AllowBlazorApp");
app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();

app.Run();

static X509Certificate2 LoadCertificate(string certFile, string pass)
{
    return new X509Certificate2(certFile, pass, X509KeyStorageFlags.MachineKeySet | X509KeyStorageFlags.PersistKeySet | X509KeyStorageFlags.Exportable);
}

static X509Certificate2 LoadCertificateFromBase64(string certFile64, string pass)
{
    byte[] certBytes = Convert.FromBase64String(certFile64);
    return new X509Certificate2(certBytes, pass, X509KeyStorageFlags.MachineKeySet | X509KeyStorageFlags.Exportable);
}