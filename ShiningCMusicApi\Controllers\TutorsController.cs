using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShiningCMusicApi.Services.Implementations;
using ShiningCMusicApi.Services.Interfaces;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class TutorsController : ControllerBase
    {
        private readonly ITutorService _tutorService;
        private readonly ILessonService _lessonService;
        private readonly ILogger<TutorsController> _logger;

        public TutorsController(ITutorService tutorService, ILessonService lessonService, ILogger<TutorsController> logger)
        {
            _tutorService = tutorService;
            _lessonService = lessonService;
            _logger = logger;
        }

        // GET: api/tutors
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Tutor>>> GetTutors()
        {
            try
            {
                var tutors = await _tutorService.GetTutorsAsync();
                return Ok(tutors);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving tutors");
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/tutors/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<Tutor>> GetTutor(int id)
        {
            try
            {
                var tutor = await _tutorService.GetTutorAsync(id);
                if (tutor == null)
                {
                    return NotFound(new { message = "Tutor not found" });
                }
                return Ok(tutor);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving tutor");
                return StatusCode(500, "Internal server error");
            }
        }

        // POST: api/tutors
        [HttpPost]
        public async Task<ActionResult<Tutor>> CreateTutor([FromBody] Tutor tutor)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(tutor.TutorName))
                {
                    return BadRequest(new { message = "Tutor name is required" });
                }

                var createdTutor = await _tutorService.CreateTutorAsync(tutor);
                return CreatedAtAction(nameof(GetTutor), new { id = createdTutor.TutorId }, createdTutor);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating tutor");
                return StatusCode(500, "Internal server error");
            }
        }

        // PUT: api/tutors/{id}
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateTutor(int id, [FromBody] Tutor tutor)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(tutor.TutorName))
                {
                    return BadRequest(new { message = "Tutor name is required" });
                }

                var success = await _tutorService.UpdateTutorAsync(id, tutor);
                if (success)
                {
                    return Ok(new { message = "Tutor updated successfully" });
                }
                return NotFound(new { message = "Tutor not found" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating tutor");
                return StatusCode(500, "Internal server error");
            }
        }

        // DELETE: api/tutors/{id}
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteTutor(int id)
        {
            try
            {
                var success = await _tutorService.DeleteTutorAsync(id);
                if (success)
                {
                    return Ok(new { message = "Tutor deleted successfully" });
                }
                return NotFound(new { message = "Tutor not found" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting tutor");
                return StatusCode(500, "Internal server error");
            }
        }

        // PATCH: api/tutors/{id}/color
        [HttpPatch("{id}/color")]
        public async Task<IActionResult> UpdateTutorColor(int id, [FromBody] UpdateTutorColorRequest request)
        {
            try
            {
                var success = await _tutorService.UpdateTutorColorAsync(id, request.Color);
                if (success)
                {
                    return Ok(new { message = "Tutor color updated successfully" });
                }
                return NotFound(new { message = "Tutor not found" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating tutor color");
                return StatusCode(500, new { message = ex.Message });
            }
        }

        // GET: api/tutors/{id}/remaining-lessons
        [HttpGet("{id}/remaining-lessons")]
        public async Task<ActionResult<int>> GetRemainingLessons(int id)
        {
            try
            {
                _logger.LogInformation("Getting remaining lessons for tutor {TutorId}", id);
                var remainingLessons = await _lessonService.GetRemainingLessonsForTutorAsync(id);
                return Ok(remainingLessons);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting remaining lessons for tutor {TutorId}", id);
                return StatusCode(500, "Internal server error");
            }
        }
    }

    public class UpdateTutorColorRequest
    {
        public string Color { get; set; } = string.Empty;
    }
}
