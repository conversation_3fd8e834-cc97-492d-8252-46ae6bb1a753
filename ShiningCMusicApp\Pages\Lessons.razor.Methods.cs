using Microsoft.JSInterop;
using ShiningCMusicCommon.Models;
using ShiningCMusicApp.Components.Dialogs;
using Syncfusion.Blazor.Schedule;

namespace ShiningCMusicApp.Pages;

public partial class LessonsBase
{
    protected async Task OnSaveClick()
    {
        if (currentEditingEvent != null)
        {
            // Validate required fields
            if (currentEditingEvent.TutorId == 0 || currentEditingEvent.StudentId == 0)
            {
                await DialogService.ShowWarningAsync("Please select both a Tutor and Student for the lesson.", "Both fields are required to create a lesson.");
                return;
            }

            if (currentEditingEvent.SubjectId == 0)
            {
                await DialogService.ShowWarningAsync("Please select a subject for the lesson.", "Subject is required to create a lesson.");
                return;
            }

            if (currentEditingEvent.LocationId == 0)
            {
                await DialogService.ShowWarningAsync("Please select a location for the lesson.", "Location is required to create a lesson.");
                return;
            }

            // Note: Recurrence rule is automatically handled by SfRecurrenceEditor
            isSaving = true;
            try
            {
                // Determine if this is create or update
                bool isNewEvent = currentEditingEvent.Id == 0;

                if (isNewEvent)
                {
                    if (scheduleRef != null)
                    {
                        await scheduleRef.AddEventAsync(currentEditingEvent);
                    }
                }
                else
                {
                    if (scheduleRef != null)
                    {
                        var currentAction = scheduleRef.GetCurrentAction();
                        await scheduleRef.SaveEventAsync(currentEditingEvent, currentAction);
                    }
                }

                if (scheduleRef != null)
                {
                    scheduleRef.CloseEditor();
                }
            }
            catch (Exception ex)
            {
                await DialogService.ShowErrorAsync("Error saving lesson", ex.Message);
            }
            finally
            {
                isSaving = false;
            }
        }
    }

    protected void OnCancelClick()
    {
        // Note: Recurrence state is automatically handled by SfRecurrenceEditor
        isSaving = false;
        if (scheduleRef != null)
        {
            scheduleRef.CloseEditor();
        }
    }

    protected async Task OnCellClick(CellClickEventArgs args)
    {
        try
        {
            // Check if user can add events
            if (!CanAddEvents)
            {
                await DialogService.ShowWarningAsync("You don't have permission to create lessons.", "Please contact an administrator for access.");
                return;
            }

            args.Cancel = true; // Cancel default double-click behavior

            // Note: Recurrence state is automatically handled by SfRecurrenceEditor

            // Open editor for creating new event
            if (scheduleRef != null)
            {
                await scheduleRef.OpenEditorAsync(args, CurrentAction.Add);
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error in OnCellClick: {ex.Message}");
        }
    }

    protected void OnPopupOpen(PopupOpenEventArgs<ScheduleEvent> args)
    {
        // Reset all previous snapshots
        originalRules.Clear();

        if (args.Type == PopupType.Editor)
        {
            isEditMode = args.Data?.Id != 0 && args.Data?.Id != null;

            // Capture the original duration for automatic end time adjustment
            if (args.Data != null)
            {
                var duration = args.Data.EndTime - args.Data.StartTime;
                originalEventDuration = duration > TimeSpan.Zero ? duration : TimeSpan.FromHours(1);
            }

            // If editing following events, we need to store the original recurrence rule
            if (pendingAction == CurrentAction.EditFollowingEvents)
            {
                if (!string.IsNullOrEmpty(args.Data?.RecurrenceRule))
                    originalRules[args.Data.Id] = args.Data.RecurrenceRule;
            }

            // Detect if editing the entire series
            if (pendingAction == CurrentAction.EditSeries)
            {
                if (args.Data != null)
                { 
                    // Reload original series data from your data source
                    var originalSeries = scheduleEvents.FirstOrDefault(e => e.Id == args.Data.RecurrenceID);

                    if (originalSeries != null)
                    {
                        // Restore original values so occurrence changes don't appear
                        args.Data.Subject = originalSeries.Subject;
                        args.Data.StartTime = originalSeries.StartTime;
                        args.Data.EndTime = originalSeries.EndTime;
                        args.Data.Description = originalSeries.Description;
                        args.Data.StudentId = originalSeries.StudentId;
                        args.Data.TutorId = originalSeries.TutorId;
                        args.Data.StudentName = originalSeries.StudentName;
                        args.Data.TutorName = originalSeries.TutorName;
                        args.Data.Location = originalSeries.Location;
                        args.Data.IsTrial = originalSeries.IsTrial;
                        args.Data.IsCancelled = originalSeries.IsCancelled;
                    }                            
                }
            }
        }
    }

    protected async Task OnEventDoubleClick(EventClickArgs<ScheduleEvent> args)
    {
        try
        {
            // Check if user can edit events
            if (!CanEditEvents)
            {
                // await JSRuntime.InvokeVoidAsync("alert", "You don't have permission to edit lessons.");
                return;
            }

            args.Cancel = true; // Cancel default editor opening behavior

            if (scheduleRef != null)
            {
                if (!string.IsNullOrEmpty(args.Event.RecurrenceRule))
                {
                    // Show custom dialog for recurring events
                    if (recurringEventActionDialog != null)
                    {
                        var editOption = await recurringEventActionDialog.ShowEditAsync();

                        if (editOption.HasValue)
                        {
                            pendingAction = editOption.Value switch
                            {
                                RecurringEventActionDialog.EditOption.EditEvent => CurrentAction.EditOccurrence,
                                RecurringEventActionDialog.EditOption.EditFollowingEvents => CurrentAction.EditFollowingEvents,
                                RecurringEventActionDialog.EditOption.EditSeries => CurrentAction.EditSeries,
                                _ => CurrentAction.EditOccurrence
                            };

                            await scheduleRef.OpenEditorAsync(args.Event, pendingAction.Value);
                        }
                    }
                }
                else
                {
                    // Non-recurring event - just open editor for that event
                    pendingAction = CurrentAction.Save;
                    await scheduleRef.OpenEditorAsync(args.Event, pendingAction.Value);
                }
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error in OnEventClick: {ex.Message}");
        }
    }

    // QuickInfo footer button handlers
    protected async Task OnQuickEditClick(ScheduleEvent? eventData)
    {
        if (eventData != null && scheduleRef != null)
        {
            if (eventData.RecurrenceRule != null)
            {
                // Show custom dialog for recurring events
                if (recurringEventActionDialog != null)
                {
                    var editOption = await recurringEventActionDialog.ShowAsync(RecurringEventActionDialog.ActionType.Edit);

                    if (editOption.HasValue)
                    {
                        pendingAction = editOption.Value switch
                        {
                            RecurringEventActionDialog.ActionOption.EditEvent => CurrentAction.EditOccurrence,
                            RecurringEventActionDialog.ActionOption.EditFollowingEvents => CurrentAction.EditFollowingEvents,
                            RecurringEventActionDialog.ActionOption.EditSeries => CurrentAction.EditSeries,
                            _ => CurrentAction.EditOccurrence
                        };

                        await scheduleRef.OpenEditorAsync(eventData, pendingAction.Value);
                    }
                }
            }
            else
            {
                // Non-recurring event - just open editor for that event
                pendingAction = CurrentAction.Save;
                await scheduleRef.OpenEditorAsync(eventData, pendingAction.Value);
            }
        }
    }

    protected async Task OnQuickDeleteClick(ScheduleEvent? eventData)
    {
        if (eventData != null && scheduleRef != null)
        {
            if (!string.IsNullOrEmpty(eventData.RecurrenceRule))
            {
                // Show custom dialog for recurring events
                if (recurringEventActionDialog != null)
                {
                    var deleteOption = await recurringEventActionDialog.ShowAsync(RecurringEventActionDialog.ActionType.Delete);

                    if (deleteOption.HasValue)
                    {
                        CurrentAction action = deleteOption.Value switch
                        {
                            RecurringEventActionDialog.ActionOption.DeleteEvent => CurrentAction.DeleteOccurrence,
                            RecurringEventActionDialog.ActionOption.DeleteFollowingEvents => CurrentAction.DeleteFollowingEvents,
                            RecurringEventActionDialog.ActionOption.DeleteSeries => CurrentAction.DeleteSeries,
                            _ => CurrentAction.DeleteOccurrence
                        };

                        // Show confirmation dialog before deleting
                        var actionText = action switch
                        {
                            CurrentAction.DeleteOccurrence => "this occurrence",
                            CurrentAction.DeleteFollowingEvents => "this and all future occurrences",
                            CurrentAction.DeleteSeries => "the entire recurring series",
                            _ => "this occurrence"
                        };

                        var message = $"Are you sure you want to delete {actionText} of the lesson '{eventData.Subject}'?";
                        var details = $"Student: {eventData.StudentName}\nTutor: {eventData.TutorName}\nTime: {eventData.StartTime:MMM dd, yyyy h:mm tt}";

                        var confirmed = await DialogService.ShowDeleteConfirmationAsync(
                            message,
                            details,
                            "Delete Lesson");

                        if (confirmed)
                        {
                            // Use Syncfusion's built-in delete functionality with the specified action
                            await scheduleRef.DeleteEventAsync(eventData, action);
                            // Close the QuickInfo popup
                            await scheduleRef.CloseQuickInfoPopupAsync();
                        }
                    }
                }
            }
            else
            {
                // Non-recurring event - show simple confirmation dialog
                var message = $"Are you sure you want to delete the lesson '{eventData.Subject}'?";
                var details = $"Student: {eventData.StudentName}\nTutor: {eventData.TutorName}\nTime: {eventData.StartTime:MMM dd, yyyy h:mm tt}";

                var confirmed = await DialogService.ShowDeleteConfirmationAsync(
                    message,
                    details,
                    "Delete Lesson");

                if (confirmed)
                {
                    // Use Syncfusion's built-in delete functionality
                    await scheduleRef.DeleteEventAsync(eventData);
                    // Close the QuickInfo popup
                    await scheduleRef.CloseQuickInfoPopupAsync();
                }
            }
        }
    }

    protected async Task OnMobileLessonClick(ScheduleEvent lesson)
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("console.log", $"Mobile lesson clicked: {lesson.Id} - {lesson.SubjectName}");

            // Try to open quick view
            await OpenLessonQuickView(lesson);
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error in OnMobileLessonClick: {ex.Message}");
        }
    }

    protected async Task OpenLessonQuickView(ScheduleEvent lesson)
    {
        // Create a custom quick info popup that matches the existing template
        await ShowCustomQuickInfo(lesson);
    }

    // Static methods for JavaScript interop
    [JSInvokable]
    protected static async Task EditLessonFromQuickInfo(int lessonId)
    {
        // Find the current page instance and call the edit method
        var currentPage = GetCurrentPageInstance();
        if (currentPage != null)
        {
            await currentPage.EditLessonById(lessonId);
        }
    }

    [JSInvokable]
    protected static async Task DeleteLessonFromQuickInfo(int lessonId)
    {
        // Find the current page instance and call the delete method
        var currentPage = GetCurrentPageInstance();
        if (currentPage != null)
        {
            await currentPage.DeleteLessonById(lessonId);
        }
    }

    protected static LessonsBase? GetCurrentPageInstance()
    {
        // This is a simplified approach - in a real application you might use a service
        // For now, we'll use a static reference
        return _currentInstance;
    }

    protected async Task EditLessonById(int lessonId)
    {
        try
        {
            // Find the lesson in our current data
            var lesson = scheduleEvents.FirstOrDefault(e => e.Id == lessonId);
            if (lesson != null)
            {
                // Open the editor for this lesson
                if (scheduleRef != null)
                {
                    if (!string.IsNullOrEmpty(lesson.RecurrenceRule))
                    {
                        // Show custom dialog for recurring events
                        if (recurringEventActionDialog != null)
                        {
                            var editOption = await recurringEventActionDialog.ShowEditAsync();

                            if (editOption.HasValue)
                            {
                                CurrentAction action = editOption.Value switch
                                {
                                    RecurringEventActionDialog.EditOption.EditEvent => CurrentAction.EditOccurrence,
                                    RecurringEventActionDialog.EditOption.EditFollowingEvents => CurrentAction.EditFollowingEvents,
                                    RecurringEventActionDialog.EditOption.EditSeries => CurrentAction.EditSeries,
                                    _ => CurrentAction.EditOccurrence
                                };

                                await scheduleRef.OpenEditorAsync(lesson, action);
                            }
                        }
                    }
                    else
                    {
                        // Non-recurring event - just open editor for that event
                        await scheduleRef.OpenEditorAsync(lesson, CurrentAction.Save);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error editing lesson: {ex.Message}");
        }
    }

    protected async Task DeleteLessonById(int lessonId)
    {
        try
        {
            // Find the lesson in our current data
            var lesson = scheduleEvents.FirstOrDefault(e => e.Id == lessonId);
            if (lesson == null) return;

            if (!string.IsNullOrEmpty(lesson.RecurrenceRule))
            {
                // Show custom dialog for recurring events
                if (recurringEventActionDialog != null)
                {
                    var deleteOption = await recurringEventActionDialog.ShowAsync(RecurringEventActionDialog.ActionType.Delete);

                    if (deleteOption.HasValue)
                    {
                        CurrentAction action = deleteOption.Value switch
                        {
                            RecurringEventActionDialog.ActionOption.DeleteEvent => CurrentAction.DeleteOccurrence,
                            RecurringEventActionDialog.ActionOption.DeleteFollowingEvents => CurrentAction.DeleteFollowingEvents,
                            RecurringEventActionDialog.ActionOption.DeleteSeries => CurrentAction.DeleteSeries,
                            _ => CurrentAction.DeleteOccurrence
                        };

                        // Show confirmation dialog before deleting
                        var actionText = action switch
                        {
                            CurrentAction.DeleteOccurrence => "this occurrence",
                            CurrentAction.DeleteFollowingEvents => "this and all future occurrences",
                            CurrentAction.DeleteSeries => "the entire recurring series",
                            _ => "this occurrence"
                        };

                        var message = $"Are you sure you want to delete {actionText} of the lesson '{lesson.Subject}'?";
                        var details = $"Student: {lesson.StudentName}\nTutor: {lesson.TutorName}\nTime: {lesson.StartTime:MMM dd, yyyy h:mm tt}";

                        var confirmed = await DialogService.ShowDeleteConfirmationAsync(
                            message,
                            details,
                            "Delete Lesson");

                        if (confirmed && scheduleRef != null)
                        {
                            // Use Syncfusion's built-in delete functionality with the specified action
                            await scheduleRef.DeleteEventAsync(lesson, action);
                        }
                    }
                }
            }
            else
            {
                // Non-recurring event - show simple confirmation dialog
                var message = "Are you sure you want to delete this lesson?";
                var details = $"Student: {lesson.StudentName}\nTutor: {lesson.TutorName}\nTime: {lesson.StartTime:MMM dd, yyyy h:mm tt}";

                var confirmed = await DialogService.ShowDeleteConfirmationAsync(
                    message,
                    details,
                    "Delete Lesson");

                if (confirmed)
                {
                    // Delete the lesson using the API directly for non-recurring events
                    var success = await BffDashboard.DeleteLessonAsync(lesson.Id);
                    if (success)
                    {
                        await JSRuntime.InvokeVoidAsync("console.log", $"Deleted lesson with ID: {lesson.Id}");
                        await LoadData(); // Refresh the data
                        StateHasChanged(); // Update the UI
                    }
                    else
                    {
                        await DialogService.ShowErrorAsync("Failed to delete lesson", "Please try again.");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error deleting lesson: {ex.Message}");
        }
    }
}
