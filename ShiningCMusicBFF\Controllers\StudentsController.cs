using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShiningCMusicBFF.Services;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicBFF.Controllers
{
    [ApiController]
    [Route("bff/students")]
    [Authorize]
    public class StudentsController : ControllerBase
    {
        private readonly ApiClientService _apiClient;
        private readonly ILogger<StudentsController> _logger;

        public StudentsController(ApiClientService apiClient, ILogger<StudentsController> logger)
        {
            _apiClient = apiClient;
            _logger = logger;
        }

        // GET: bff/students
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Student>>> GetStudents()
        {
            try
            {
                _logger.LogInformation("Fetching students from API");
                var students = await _apiClient.GetJsonAsync<List<Student>>("students");
                return Ok(students ?? new List<Student>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving students from API");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // GET: bff/students/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<Student>> GetStudent(int id)
        {
            try
            {
                _logger.LogInformation("Fetching student {StudentId} from API", id);
                var student = await _apiClient.GetJsonAsync<Student>($"students/{id}");
                
                if (student == null)
                {
                    return NotFound(new { message = "Student not found" });
                }

                return Ok(student);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving student {StudentId} from API", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // POST: bff/students
        [HttpPost]
        public async Task<ActionResult<Student>> CreateStudent([FromBody] Student student)
        {
            try
            {
                _logger.LogInformation("Creating student via API");
                var response = await _apiClient.PostAsync("students", student);
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var createdStudent = System.Text.Json.JsonSerializer.Deserialize<Student>(content, new System.Text.Json.JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return CreatedAtAction(nameof(GetStudent), new { id = createdStudent?.StudentId }, createdStudent);
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to create student. Status: {StatusCode}, Error: {Error}", response.StatusCode, errorContent);
                return StatusCode((int)response.StatusCode, new { message = "Failed to create student", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating student via API");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // PUT: bff/students/{id}
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateStudent(int id, [FromBody] Student student)
        {
            try
            {
                _logger.LogInformation("Updating student {StudentId} via API", id);
                var response = await _apiClient.PutAsync($"students/{id}", student);
                
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { message = "Student updated successfully" });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to update student {StudentId}. Status: {StatusCode}, Error: {Error}", id, response.StatusCode, errorContent);
                
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "Student not found" });
                }

                return StatusCode((int)response.StatusCode, new { message = "Failed to update student", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating student {StudentId} via API", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // DELETE: bff/students/{id}
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteStudent(int id)
        {
            try
            {
                _logger.LogInformation("Deleting student {StudentId} via API", id);
                var response = await _apiClient.DeleteAsync($"students/{id}");
                
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { message = "Student deleted successfully" });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to delete student {StudentId}. Status: {StatusCode}, Error: {Error}", id, response.StatusCode, errorContent);
                
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "Student not found" });
                }

                return StatusCode((int)response.StatusCode, new { message = "Failed to delete student", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting student {StudentId} via API", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // GET: bff/students/by-subject/{subjectId}
        [HttpGet("by-subject/{subjectId}")]
        public async Task<ActionResult<IEnumerable<Student>>> GetStudentsBySubject(int subjectId)
        {
            try
            {
                _logger.LogInformation("Fetching students by subject {SubjectId} from API", subjectId);
                var students = await _apiClient.GetJsonAsync<List<Student>>($"students/by-subject/{subjectId}");
                return Ok(students ?? new List<Student>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving students by subject {SubjectId} from API", subjectId);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // GET: bff/students/archived
        [HttpGet("archived")]
        public async Task<ActionResult<IEnumerable<Student>>> GetArchivedStudents()
        {
            try
            {
                _logger.LogInformation("Fetching archived students from API");
                var students = await _apiClient.GetJsonAsync<List<Student>>("students/archived");
                return Ok(students ?? new List<Student>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving archived students from API");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // PUT: bff/students/{id}/archive
        [HttpPut("{id}/archive")]
        public async Task<IActionResult> ArchiveStudent(int id)
        {
            try
            {
                _logger.LogInformation("Archiving student {StudentId} via API", id);
                var response = await _apiClient.PutAsync($"students/{id}/archive", null);
                
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { message = "Student archived successfully" });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to archive student {StudentId}. Status: {StatusCode}, Error: {Error}", id, response.StatusCode, errorContent);
                
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "Student not found" });
                }

                return StatusCode((int)response.StatusCode, new { message = "Failed to archive student", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error archiving student {StudentId} via API", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // PUT: bff/students/{id}/restore
        [HttpPut("{id}/restore")]
        public async Task<IActionResult> RestoreStudent(int id)
        {
            try
            {
                _logger.LogInformation("Restoring student {StudentId} via API", id);
                var response = await _apiClient.PutAsync($"students/{id}/restore", null);

                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { message = "Student restored successfully" });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to restore student {StudentId}. Status: {StatusCode}, Error: {Error}", id, response.StatusCode, errorContent);

                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "Student not found" });
                }

                return StatusCode((int)response.StatusCode, new { message = "Failed to restore student", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error restoring student {StudentId} via API", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // GET: bff/students/{id}/remaining-lessons
        [HttpGet("{id}/remaining-lessons")]
        public async Task<ActionResult<int>> GetRemainingLessons(int id)
        {
            try
            {
                _logger.LogInformation("Fetching remaining lessons for student {StudentId} from API", id);
                var remainingLessons = await _apiClient.GetJsonAsync<int>($"students/{id}/remaining-lessons");
                return Ok(remainingLessons);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving remaining lessons for student {StudentId} from API", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }
    }
}
